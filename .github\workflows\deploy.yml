# .github/workflows/deploy.yml
name: IzeFe CI/CD Pipeline

on:
  push:
    branches:
      - main # main branch'ine yapılan her push bu workflow'u tetikler

env:
  DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
  DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
  DOCKER_IMAGE_BACKEND: ${{ secrets.DOCKER_USERNAME }}/izefe-back:latest # Kendi Docker Hub kullanıcı adınız
  DOCKER_IMAGE_FRONTEND: ${{ secrets.DOCKER_USERNAME }}/izefe-front:latest # Kendi Docker Hub kullanıcı adınız
  PROJECT_SERVER_PATH: ${{ secrets.PROJECT_PATH_ON_SERVER }}

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest # GitHub Actions'ın çalışacağı sanal makine
 
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ env.DOCKER_PASSWORD }}

      # --- Backend (dotnet) Docker Image Build and Push ---
      - name: Build and Push Backend Docker image
        run: |
          # Ensure you are in the root of the repository to correctly reference Dockerfile path
          docker build -t ${{ env.DOCKER_IMAGE_BACKEND }} -f ./backend/Dockerfile . # backend projenizin Dockerfile yolu
          docker push ${{ env.DOCKER_IMAGE_BACKEND }}

      # --- Frontend (Next.js) Docker Image Build and Push ---
      - name: Build and Push Frontend Docker image
        run: |
          docker build -t ${{ env.DOCKER_IMAGE_FRONTEND }} -f ./client/Dockerfile . # frontend projenizin Dockerfile yolu
          docker push ${{ env.DOCKER_IMAGE_FRONTEND }}

      # --- Deploy to Ubuntu Server via SSH ---
      - name: Deploy to Ubuntu Server
        uses: appleboy/ssh-action@v0.1.8 # SSH bağlantısı için popüler bir action
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          passphrase: ${{ secrets.SSH_PASSPHRASE }} # Eğer varsa
          script: |
            # Navigate to the project directory on the server
            cd ${{ env.PROJECT_SERVER_PATH }}
            
             # --- ÖNEMLİ: BU KISIM GÜNCELLENMELİ ---
            # 1. Server'da Docker Hub'a Login ol
            echo "${{ secrets.DOCKER_PASSWORD }}" | sudo docker login -u "${{ env.DOCKER_USERNAME }}" --password-stdin
            
            # 2. Eskiden projenin server'a klonlandığı/çekildiği kısmı kaldırın veya yorum satırına alın
            #    Çünkü actions/checkout@v3 zaten kodu Actions runner'ına çekiyor,
            #    ve sizin docker-compose.yml dosyanız zaten server'da olmalı.
            #    Ayrıca git clone/pull hataları alıyoruz.
            #    Eğer docker-compose.yml yoksa server'da, bu dosyanın Actions'tan server'a kopyalanması gerekir,
            #    ancak sizin durumunuzda zaten vardı.
            # sudo git clone https://github.com/buraxta/izefe-form-yeni.git . || sudo git pull
            
            # 3. Docker Compose komutlarını doğru şekilde kullanın (docker compose)
            sudo docker compose pull # Docker Hub'daki yeni imajları çeker
            sudo docker compose up -d --remove-orphans # Container'ları günceller ve yeniden başlatır
            
            # 4. Docker login bilgilerini temizle
            sudo docker logout