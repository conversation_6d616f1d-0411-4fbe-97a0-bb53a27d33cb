using System;
using System.Collections.Generic;

namespace backend.Models;

/// <summary>
/// Represents a parent or guardian of a user in the izefe form system
/// </summary>
public class Parents
{
    /// <summary>
    /// The unique identifier for the parent
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// The full name of the parent
    /// </summary>
    public string FullName { get; set; }

    /// <summary>
    /// The mobile phone number of the parent
    /// </summary>
    public string MobilePhone { get; set; }

    /// <summary>
    /// The height of the mother in centimeters
    /// </summary>
    public double MotherHeight { get; set; }

    /// <summary>
    /// The height of the father in centimeters
    /// </summary>
    public double FatherHeight { get; set; }

    /// <summary>
    /// The relationship type of the parent to the child (e.g., <PERSON>, Father, Guardian)
    /// </summary>
    public string RelationshipType { get; set; }

    /// <summary>
    /// Indicates whether the parent has given approval for the registration
    /// </summary>
    public bool ApprovalGiven { get; set; }

    /// <summary>
    /// The date when the parent gave their approval
    /// </summary>
    public DateTime? ApprovalDate { get; set; }

    /// <summary>
    /// Collection of users associated with this parent
    /// </summary>
    public ICollection<User> Users { get; set; } = new List<User>();
}
