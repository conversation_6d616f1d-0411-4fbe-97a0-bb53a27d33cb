"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/favicon.ico/route";
exports.ids = ["app/favicon.ico/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_filePath_C_3A_5CUsers_5Cburak_5CDesktop_5Cizefe_form_yeni_5Cclient_5Csrc_5Capp_5Cfavicon_ico_isDynamicRouteExtension_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?filePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/favicon.ico/route\",\n        pathname: \"/favicon.ico\",\n        filename: \"favicon\",\n        bundlePath: \"app/favicon.ico/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?filePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_filePath_C_3A_5CUsers_5Cburak_5CDesktop_5Cizefe_form_yeni_5Cclient_5Csrc_5Capp_5Cfavicon_ico_isDynamicRouteExtension_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__ ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* static asset route */\n\n\nconst contentType = \"image/x-icon\"\nconst buffer = Buffer.from(\"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\", 'base64'\n  )\n\nif (false) {}\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__\n");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5Cizefe-form-yeni%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();